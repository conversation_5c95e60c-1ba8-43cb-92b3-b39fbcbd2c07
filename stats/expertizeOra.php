<?php
// Check for Excel export first to avoid header issues
if (isset($_GET['export']) && $_GET['export'] === 'excel') {
    require_once '../inc/cfg_functions.php';
    require_once '../inc/cfg_db.php';
    require_once 'excel_export.php';

    $dbConnection = DatabasePool::getConnection();

    // Primul grafic - distribuția pe ore
    $selectperOraAstazi = "select count(*) nr_expertize, hour(dataDesemnare) moment
    from exp_jud.expertize
    where date(dataDesemnare) = date(now())
    group by hour(dataDesemnare)
    order by dataDesemnare";
    $selectperOraAstazi = $dbConnection->query($selectperOraAstazi)->fetchAll();

    $labelsOre = [];
    $valuesOre = [];
    $nr_expertizeTotalOre = 0;

    foreach ($selectperOraAstazi as $row) {
        $labelsOre[] = "Ora {$row['moment']}";
        $valuesOre[] = (int)$row['nr_expertize'];
        $nr_expertizeTotalOre += $row['nr_expertize'];
    }

    // Al doilea grafic - distribuția pe zile
    $selectPerZileLunaCurenta = "
    SELECT DATE(dataDesemnare) AS moment, COUNT(*) AS nr_expertize
    FROM exp_jud.expertize
    WHERE dataDesemnare >= CURDATE() - INTERVAL 30 DAY
    GROUP BY DATE(dataDesemnare)
    ORDER BY moment;";
    $selectPerZileLunaCurenta = $dbConnection->query($selectPerZileLunaCurenta)->fetchAll();

    $labelsZile = [];
    $valuesZile = [];
    $nr_expertizeTotalLuna = 0;

    foreach ($selectPerZileLunaCurenta as $row) {
        $labelsZile[] = date('d.m.Y', strtotime($row['moment']));
        $valuesZile[] = (int)$row['nr_expertize'];
        $nr_expertizeTotalLuna += $row['nr_expertize'];
    }

    //grafic 3 - total / luni si medie lunara
    $selectTotalLuniSiMedie = "
    SELECT
        YEAR(dataDesemnare) AS an,
        MONTH(dataDesemnare) AS luna,
        COUNT(*) AS numar_expertize
    FROM exp_jud.expertize
    WHERE dataDesemnare IS NOT NULL
        AND dataDesemnare >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY YEAR(dataDesemnare), MONTH(dataDesemnare)
    ORDER BY an ASC, luna ASC;";

    $rezultate = $dbConnection->query($selectTotalLuniSiMedie)->fetchAll(PDO::FETCH_ASSOC);

    $labelsLuni = [];
    $valuesLuni = [];
    $totalExpertize = 0;
    $nrLuni = count($rezultate);

    foreach ($rezultate as $row) {
        $monthYear = date('F Y', strtotime($row['an'] . '-' . $row['luna'] . '-01'));
        $labelsLuni[] = $monthYear;
        $valuesLuni[] = (int)$row['numar_expertize'];
        $totalExpertize += (int)$row['numar_expertize'];
    }

    $medieLunara = $nrLuni > 0 ? round($totalExpertize / $nrLuni, 2) : 0;
    $mediaLine = array_fill(0, count($labelsLuni), $medieLunara);

    // Prepare data for multiple sheets
    $sheets = [
        [
            'name' => 'Expertize pe ore',
            'title' => 'Distribuția expertizelor pe ore (astăzi ' . date('d.m.Y') . ')',
            'description' => 'Total expertize: ' . $nr_expertizeTotalOre,
            'headers' => ['Ora', 'Număr expertize'],
            'data' => formatDataForExcel($labelsOre, $valuesOre)
        ],
        [
            'name' => 'Expertize pe zile',
            'title' => 'Distribuția expertizelor pe zile (ultimele 30 de zile)',
            'description' => 'Total expertize: ' . $nr_expertizeTotalLuna,
            'headers' => ['Data', 'Număr expertize'],
            'data' => formatDataForExcel($labelsZile, $valuesZile)
        ],
        [
            'name' => 'Expertize pe luni',
            'title' => 'Distribuția expertizelor pe luni (ultimele 12 luni)',
            'description' => 'Total expertize: ' . $totalExpertize . ' | Media lunară: ' . $medieLunara,
            'headers' => ['Luna', 'Număr expertize', 'Media lunară'],
            'data' => array_map(function($label, $value) use ($medieLunara) {
                return [$label, $value, $medieLunara];
            }, $labelsLuni, $valuesLuni)
        ]
    ];

    // Release database connection
    DatabasePool::releaseConnection($dbConnection);

    // Export to Excel with multiple sheets
    exportToExcelMultipleSheets($sheets, 'statistici-expertize-' . date('Y-m-d') . '.xls');
    exit;
} else {
    header('Content-Type: application/json; charset=UTF-8');

    require_once '../inc/cfg_functions.php';
    require_once '../inc/cfg_db.php';
    require_once 'excel_export.php';
    $dbConnection = DatabasePool::getConnection();

// Primul grafic - distribuția pe ore
    $selectperOraAstazi = "select count(*) nr_expertize, hour(dataDesemnare) moment
from exp_jud.expertize
where date(dataDesemnare) = date(now())
group by hour(dataDesemnare)
order by dataDesemnare";
    $selectperOraAstazi = $dbConnection->query($selectperOraAstazi)->fetchAll();

    $labelsOre = [];
    $valuesOre = [];
    $nr_expertizeTotalOre = 0;

    foreach ($selectperOraAstazi as $row) {
        $labelsOre[] = "Ora {$row['moment']}";
        $valuesOre[] = (int)$row['nr_expertize'];
        $nr_expertizeTotalOre += $row['nr_expertize'];
    }

// Al doilea grafic - distribuția pe zile
    $selectPerZileLunaCurenta = "
SELECT DATE(dataDesemnare) AS moment, COUNT(*) AS nr_expertize
FROM exp_jud.expertize
WHERE dataDesemnare >= CURDATE() - INTERVAL 30 DAY
GROUP BY DATE(dataDesemnare)
ORDER BY moment;";
    $selectPerZileLunaCurenta = $dbConnection->query($selectPerZileLunaCurenta)->fetchAll();

    $labelsZile = [];
    $valuesZile = [];
    $nr_expertizeTotalLuna = 0;

    foreach ($selectPerZileLunaCurenta as $row) {
        $labelsZile[] = date('d.m.Y', strtotime($row['moment']));
        $valuesZile[] = (int)$row['nr_expertize'];
        $nr_expertizeTotalLuna += $row['nr_expertize'];
    }

//grafic 3 - total / luni si medie lunara
    $selectTotalLuniSiMedie = "
SELECT
    YEAR(dataDesemnare) AS an,
    MONTH(dataDesemnare) AS luna,
    COUNT(*) AS numar_expertize
FROM exp_jud.expertize
WHERE dataDesemnare IS NOT NULL
    AND dataDesemnare >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
GROUP BY YEAR(dataDesemnare), MONTH(dataDesemnare)
ORDER BY an ASC, luna ASC;";

    $rezultate = $dbConnection->query($selectTotalLuniSiMedie)->fetchAll(PDO::FETCH_ASSOC);

    $labelsLuni = [];
    $valuesLuni = [];
    $totalExpertize = 0;
    $nrLuni = count($rezultate);

    foreach ($rezultate as $row) {
        $monthYear = date('F Y', strtotime($row['an'] . '-' . $row['luna'] . '-01'));
        $labelsLuni[] = $monthYear;
        $valuesLuni[] = (int)$row['numar_expertize'];
        $totalExpertize += (int)$row['numar_expertize'];
    }

    $medieLunara = $nrLuni > 0 ? round($totalExpertize / $nrLuni, 2) : 0;
    $mediaLine = array_fill(0, count($labelsLuni), $medieLunara);

    DatabasePool::releaseConnection($dbConnection);


    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Add export button to the response
        $exportButton = '<div class="text-right mb-3">
        <a href="stats/expertizeOra.php?export=excel" class="btn btn-sm btn-outline-success">
            <i class="fas fa-file-excel mr-1"></i> Export Excel
        </a>
    </div>';

        $response = [
            'exportButton' => $exportButton,
            'charts' => [
                'ore' => [
                    'type' => 'bar',
                    'labels' => $labelsOre,
                    'datasets' => [
                        [
                            'label' => 'Număr expertize',
                            'data' => $valuesOre,
                            'backgroundColor' => 'rgba(54, 162, 235, 0.5)',
                            'borderColor' => 'rgba(54, 162, 235, 1)',
                            'borderWidth' => 1
                        ]
                    ],
                    'options' => [
                        'responsive' => true,
                        'scales' => [
                            'y' => [
                                'beginAtZero' => true,
                                'ticks' => [
                                    'stepSize' => 1
                                ]
                            ]
                        ],
                        'plugins' => [
                            'title' => [
                                'display' => true,
                                'text' => 'Distribuția expertizelor pe ore (astăzi ' . date('d.m.Y') . ') - Total expertize: ' . $nr_expertizeTotalOre
                            ]
                        ]
                    ]
                ],
                'zile' => [
                    'type' => 'bar',
                    'labels' => $labelsZile,
                    'datasets' => [
                        [
                            'label' => 'Număr expertize',
                            'data' => $valuesZile,
                            'backgroundColor' => 'rgba(75, 192, 192, 0.5)',
                            'borderColor' => 'rgba(75, 192, 192, 1)',
                            'borderWidth' => 1
                        ]
                    ],
                    'options' => [
                        'responsive' => true,
                        'scales' => [
                            'y' => [
                                'beginAtZero' => true,
                                'ticks' => [
                                    'stepSize' => 1
                                ]
                            ]
                        ],
                        'plugins' => [
                            'title' => [
                                'display' => true,
                                'text' => 'Distribuția expertizelor pe zile (ultimele 30 de zile) - Total expertize: ' . $nr_expertizeTotalLuna
                            ]
                        ]
                    ]
                ],
                'luni' => [
                    'type' => 'bar',
                    'labels' => array_values($labelsLuni),
                    'datasets' => [
                        [
                            'type' => 'bar',
                            'label' => 'Număr expertize pe lună',
                            'data' => array_values($valuesLuni),
                            'backgroundColor' => 'rgba(75, 192, 192, 0.5)',
                            'borderColor' => 'rgba(75, 192, 192, 1)',
                            'borderWidth' => 1,
                            'yAxisID' => 'y'
                        ],
                        [
                            'type' => 'line',
                            'label' => 'Media lunară',
                            'data' => array_values($valuesLuni),
                            'borderColor' => 'rgba(255, 99, 132, 1)',
                            'borderWidth' => 2,
                            'borderDash' => [5, 5],
                            'pointRadius' => 4,
                            'pointBackgroundColor' => 'rgba(255, 99, 132, 1)',
                            'fill' => false,
                            'yAxisID' => 'y'
                        ]
                    ],
                    'options' => [
                        'responsive' => true,
                        'maintainAspectRatio' => true,
                        'interaction' => [
                            'intersect' => false,
                            'mode' => 'index'
                        ],
                        'plugins' => [
                            'title' => [
                                'display' => true,
                                'text' => 'Total expertize: ' . $totalExpertize . ' | Media lunară: ' . $medieLunara
                            ],
                            'legend' => [
                                'position' => 'top'
                            ],
                            'tooltip' => [
                                'enabled' => true,
                                'mode' => 'index'
                            ]
                        ],
                        'scales' => [
                            'y' => [
                                'type' => 'linear',
                                'display' => true,
                                'position' => 'left',
                                'beginAtZero' => true,
                                'grid' => [
                                    'drawBorder' => false,
                                    'color' => 'rgba(0, 0, 0, 0.1)'
                                ]
                            ],
                            'x' => [
                                'grid' => [
                                    'display' => false
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        echo json_encode($response);
        exit;
    } else {
        http_response_code(400);
        echo json_encode(["error" => "Cerere invalidă"]);
        exit;
    }

}
?>